import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  ArrowLeft,
  MapPin,
  Calendar,
  DollarSign,
  Clock,
  User,
  Star,
  MessageSquare,
  Phone,
  Shield,
  Camera,
  EyeOff
} from 'lucide-react';
import { JobDataType } from './Jobs';
import { apiService } from '@/services/api';
import { formatJobTimeline } from '@/utils/jobCardUtils';
import { categories } from '@/components/CategoryList';
import { useAuth } from '@/features/auth/hooks/useAuth';

// User type
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: {
    id: number;
    name: string;
    guard_name: string;
  }
}

// Auth Protected Image component
interface AuthProtectedImageProps {
  src: string;
  alt: string;
  className?: string;
  jobId: string;
}

const AuthProtectedImage: React.FC<AuthProtectedImageProps> = ({ src, alt, className, jobId }) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleImageClick = () => {
    if (!user) {
      // Redirect to auth page with redirect URL
      navigate(`/auth?redirectUrl=/job/${jobId}`);
    }
  };

  return (
    <div className="relative w-full h-full">
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-cover ${!user ? 'blur-md' : ''} ${className || ''}`}
      />
      {!user && (
        <div 
          className="absolute inset-0 bg-black/40 flex flex-col items-center justify-center cursor-pointer"
          onClick={handleImageClick}
        >
          <EyeOff className="h-6 w-6 text-white mb-2" />
          <span className="text-white font-medium text-sm">Login to view</span>
        </div>
      )}
    </div>
  );
};

const JobDetails = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [job, setJob] = useState<JobDataType | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    const fetchJobDetails = async () => {
      if (!jobId) return;

      try {
        setLoading(true);
        const response = await apiService<{ data: JobDataType }>(`/api/job-bookings/${jobId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          requiresAuth: true,
          includeCredentials: true
        });

        if (response && response.data) {
          setJob(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching job details:', error);
        toast({
          title: "Error",
          description: "Failed to load job details",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchJobDetails();
  }, [jobId, toast]);

  const handleBack = () => {
    navigate('/jobs');
  };

  const handleSubmitBid = () => {
    toast({
      title: "Bid Submitted",
      description: "Your bid has been submitted successfully!",
    });
  };

  const handleViewBids = () => {
    navigate(`/job/${jobId}/bids`);
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="animate-pulse">
            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <div className="container mx-auto px-4 py-4">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
              </div>
            </div>
            <div className="container mx-auto px-4 py-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-4">
                  <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
                <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!job) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Job not found</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-4">This job may have been removed or is no longer available.</p>
            <Button onClick={handleBack}>Back to Jobs</Button>
          </div>
        </div>
      </Layout>
    );
  }

  const categoryData = categories.find(c => c?.id === job.service.category) || categories[5];
  const timeline = formatJobTimeline(job.schedule, job.createdAt);
  const serviceTasks = job.service.tasks || [];

  // Mobile Layout
  if (isMobile) {
    return (
      <Layout>
        {/* Mobile Header */}
        <div className="sticky top-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center px-4 py-3">
            <Button variant="ghost" size="icon" onClick={handleBack} className="mr-3">
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {job.service.category}
              </h1>
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <MapPin className="h-3 w-3 mr-1" />
                <span className="truncate">{job.location.city}, {job.location.state}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Content */}
        <div className="bg-gray-50 dark:bg-gray-900 min-h-screen pb-20">
          {/* Hero Section */}
          <div className="bg-white dark:bg-gray-800 p-4 mb-1">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                {categoryData?.icon && (
                  <div className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg">
                    {React.cloneElement(categoryData.icon, { className: "h-5 w-5 text-primary" })}
                  </div>
                )}
                <div>
                  <Badge variant="outline" className="text-xs">{job.service.category}</Badge>
                </div>
              </div>
              {job.status === 'open' && (
                <Badge className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs">
                  Open
                </Badge>
              )}
            </div>

            {/* Key Details Grid */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                <span className="text-sm text-gray-900 dark:text-white font-medium">{timeline}</span>
              </div>
              {job.budget && job.budget > 0 && (
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-green-700 dark:text-green-400">
                    ${job.budget.toLocaleString()}
                  </span>
                </div>
              )}
            </div>

            {/* Service Tasks */}
            {serviceTasks.length > 0 && (
              <div className="flex flex-wrap gap-1.5 mb-4">
                {serviceTasks.slice(0, 3).map((task, index) => (
                  <span key={index} className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full">
                    {task}
                  </span>
                ))}
                {serviceTasks.length > 3 && (
                  <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 px-2 py-1 rounded-full">
                    +{serviceTasks.length - 3} more
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Description */}
          {job.description && (
            <div className="bg-white dark:bg-gray-800 p-4 mb-1">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Description</h3>
              <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{job.description}</p>
            </div>
          )}

          {/* Photos */}
          {job.assets && job.assets.length > 0 && (
            <div className="bg-white dark:bg-gray-800 p-4 mb-1">
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">Photos ({job.assets.length})</h3>
              <div className="grid grid-cols-2 gap-2">
                {job.assets.slice(0, 4).map((asset, index) => (
                  <div key={asset.uuid} className="relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                    <AuthProtectedImage
                      src={`https://dash.jobon.app/storage/${asset.url}`}
                      alt={`Job photo ${index + 1}`}
                      jobId={jobId || ''}
                    />
                    {index === 3 && job.assets.length > 4 && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                        <span className="text-white font-medium">+{job.assets.length - 4}</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Customer Info */}
          <div className="bg-white dark:bg-gray-800 p-4 mb-1">
            <h3 className="font-medium text-gray-900 dark:text-white mb-3">Customer</h3>
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src="" />
                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                  {job.contact.fullName?.charAt(0) || 'C'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-medium text-gray-900 dark:text-white">{job.contact.fullName}</p>
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">4.8 • 12 jobs posted</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1">
                <MessageSquare className="h-4 w-4 mr-2" />
                Message
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Phone className="h-4 w-4 mr-2" />
                Call
              </Button>
            </div>
          </div>

          {/* Location Details */}
          <div className="bg-white dark:bg-gray-800 p-4">
            <h3 className="font-medium text-gray-900 dark:text-white mb-3">Location & Schedule</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-gray-500 dark:text-gray-400 mt-0.5" />
                <div>
                  <p className="text-gray-900 dark:text-white">{job.location.address}</p>
                  <p className="text-gray-600 dark:text-gray-400">{job.location.city}, {job.location.state} {job.location.zipCode}</p>
                </div>
              </div>
              {job.schedule?.date && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-900 dark:text-white">
                    {new Date(job.schedule.date).toLocaleDateString()} • {job.schedule.timePreference || 'Flexible'}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Footer */}
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 z-50">
          <div className="flex gap-3">
            <Button variant="outline" size="sm" className="flex-1" onClick={handleViewBids}>
              View Bids
            </Button>
            <Button onClick={handleSubmitBid} className="flex-1">
              Submit Bid
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  // Desktop Layout
  return (
    <Layout>
      {/* Desktop Header */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-4 max-w-7xl mt-20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={handleBack} className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Jobs
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center gap-3">
                {categoryData?.icon && (
                  <div className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg">
                    {React.cloneElement(categoryData.icon, { className: "h-5 w-5 text-primary" })}
                  </div>
                )}
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">{job.service.category}</h1>
                  <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      <span>{job.location.city}, {job.location.state}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{timeline}</span>
                    </div>
                    {job.budget && job.budget > 0 && (
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3 text-green-600 dark:text-green-400" />
                        <span className="font-medium text-green-700 dark:text-green-400">
                          ${job.budget.toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {job.status === 'open' && (
              <Badge className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                Open for Bids
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Desktop Content */}
      <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-4">
              {/* Service Tasks */}
              {serviceTasks.length > 0 && (
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-3">Required Services</h3>
                    <div className="flex flex-wrap gap-2">
                      {serviceTasks.map((task, index) => (
                        <span key={index} className="text-sm bg-primary/10 dark:bg-primary/20 text-primary px-3 py-1 rounded-full">
                          {task}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Description */}
              {job.description && (
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-3">Job Description</h3>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{job.description}</p>
                  </CardContent>
                </Card>
              )}

              {/* Photos */}
              {job.assets && job.assets.length > 0 && (
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Camera className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      <h3 className="font-medium text-gray-900 dark:text-white">Photos ({job.assets.length})</h3>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {job.assets.map((asset, index) => (
                        <div key={asset.uuid} className="relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 hover:opacity-90 transition-opacity cursor-pointer">
                          <AuthProtectedImage
                            src={`https://dash.jobon.app/storage/${asset.url}`}
                            alt={`Job photo ${index + 1}`}
                            jobId={jobId || ''}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Location & Schedule */}
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-4">Location & Schedule</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 text-gray-500 dark:text-gray-400 mt-1" />
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">{job.location.address}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{job.location.city}, {job.location.state} {job.location.zipCode}</p>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      {job.schedule?.date && (
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {new Date(job.schedule.date).toLocaleDateString()}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {job.schedule.timePreference || 'Flexible timing'}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-6 space-y-4">
                {/* Customer Card */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-4">Customer</h3>
                    <div className="flex items-center gap-3 mb-4">
                      <Avatar className="h-14 w-14">
                        <AvatarImage src="" />
                        <AvatarFallback className="bg-primary/10 text-primary font-medium text-lg">
                          {job.contact.fullName?.charAt(0) || 'C'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 dark:text-white">{job.contact.fullName}</p>
                        <div className="flex items-center gap-1 mb-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">4.8 rating</span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">12 jobs posted</p>
                      </div>
                    </div>

                    <div className="space-y-3 mb-4">
                      <div className="flex items-center gap-2 text-sm">
                        <Shield className="h-4 w-4 text-green-600 dark:text-green-400" />
                        <span className="text-gray-700 dark:text-gray-300">Payment verified</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-gray-700 dark:text-gray-300">Identity verified</span>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2">
                      <Button variant="outline" size="sm" className="w-full">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Send Message
                      </Button>
                      <Button variant="outline" size="sm" className="w-full">
                        <Phone className="h-4 w-4 mr-2" />
                        Call Customer
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Action Card */}
                <Card>
                  <CardContent className="p-4">
                    <div className="text-center mb-4">
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Interested in this job?</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">Submit your bid</p>
                    </div>
                    <Button onClick={handleSubmitBid} className="w-full mb-3">
                      Submit Bid
                    </Button>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1" onClick={handleViewBids}>
                        View Bids
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        Save
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default JobDetails;
